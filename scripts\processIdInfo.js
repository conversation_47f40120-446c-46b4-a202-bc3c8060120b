const fs = require('fs');
const path = require('path');

// 读取原始文本
const rawText = fs.readFileSync(path.join(__dirname, '../data/raw_idinfo.txt'), 'utf8');

// 处理文本，将"名字----身份证号码"格式转换为JSON数组
const idInfoList = rawText.split('\n')
    .filter(line => line.trim()) // 移除空行
    .map(line => {
        const [name, id] = line.split('----').map(s => s.trim());
        return { name, id };
    });

// 将处理后的数据写入JSON文件
fs.writeFileSync(
    path.join(__dirname, '../data/idInfo.json'),
    JSON.stringify(idInfoList, null, 2),
    'utf8'
);

console.log('身份证信息处理完成，已保存到 data/idInfo.json'); 