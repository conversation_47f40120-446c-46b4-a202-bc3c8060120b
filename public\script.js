let currentUsername = null;
let currentIdInfoIndex = 0;
let idInfoList = [];
let autoVerifyEnabled = false; // 默认自动实名认证功能关闭

function updateStatus(message) {
    // 过滤掉不想显示的信息
    if (message.includes('身份信息加载成功')) {
        return;
    }
    
    const status = document.getElementById('status');
    status.innerHTML += message + '\n';
}

// 删除点击标题5次激活功能
// 修改为页面加载时从服务器获取设置
async function getAutoVerifySettings() {
    try {
        const response = await fetch('/api/settings/autoVerify');
        const data = await response.json();
        autoVerifyEnabled = data.enabled;
        console.log('自动实名认证设置状态:', autoVerifyEnabled ? '已开启' : '已关闭');
    } catch (error) {
        console.error('获取自动实名认证设置失败:', error);
        autoVerifyEnabled = false;
    }
}

// 添加一个函数来随机打乱身份信息列表
function shuffleIdInfoList() {
    for (let i = idInfoList.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [idInfoList[i], idInfoList[j]] = [idInfoList[j], idInfoList[i]];
    }
}

// 加载身份信息列表
async function loadIdInfoList() {
    try {
        const response = await fetch('/data/idInfo.json');
        if (!response.ok) {
            throw new Error('加载身份信息失败');
        }
        idInfoList = await response.json();
        shuffleIdInfoList();
        return true;
    } catch (error) {
        console.error("身份信息加载失败:", error.message);
        return false;
    }
}

// 添加一个异步提示函数
function showMessage(message) {
    setTimeout(() => {
        alert(message);
    }, 100);
}

function generateRandomUsername(length = 6) {
    const random = Array.from(Array(length), () => Math.floor(Math.random() * 10)).join('');
    document.getElementById('username').value = random;
    document.getElementById('password').value = random;
}

// 添加一个全局的跳转函数
function redirectToDownload() {
    // 判断是否为iOS设备（优化检测逻辑）
    const isIOS = /iPhone|iPad|iPod|Macintosh/i.test(navigator.userAgent) && 
                 !/Chrome|Firefox|Opera|SamsungBrowser|UCBrowser|Edge/i.test(navigator.userAgent) &&
                 /AppleWebKit/i.test(navigator.userAgent);
    
    if (isIOS) {
        window.location.href = 'itms-services://?action=download-manifest&url=https://packages.jiangxtl.com/ios_qy/6003/manifest.plist';

    } else {
        window.location.href = 'https://packages.jiangxtl.com/shouyou/6004_cq_sbqjllqj_tan/6004_cq_sbqjllqj_tan_100855.apk';
    }
}

// 修改自定义弹窗函数
function showCustomAlert(message, onConfirm, username, password) {
    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    // 创建弹窗
    const dialog = document.createElement('div');
    dialog.style.cssText = `
        font-family: 'Exo 2', sans-serif;
        background: rgba(25, 28, 50, 0.5);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.15);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-width: 90vw;
        width: 360px;
        text-align: center;
        padding: 2rem;
        transform: scale(0.95);
        transition: transform 0.3s ease;
    `;

    // 标题
    const title = document.createElement('div');
    title.textContent = '注册成功';
    title.style.cssText = `
        font-family: 'Orbitron', sans-serif;
        font-size: 2rem;
        font-weight: 700;
        background: linear-gradient(45deg, #00d2ff, #6a37ff);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        margin-bottom: 1.5rem;
        text-shadow: 0 0 15px rgba(106, 55, 255, 0.4);
    `;
    dialog.appendChild(title);

    // 账号信息容器
    const infoContainer = document.createElement('div');
    infoContainer.style.cssText = `
        background: rgba(12, 18, 32, 0.6);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    `;

    // 账号
    const userDiv = document.createElement('div');
    userDiv.innerHTML = `账号: <span style="font-weight:bold; color: #e0e0ff; letter-spacing: 1px;">${username}</span>`;
    userDiv.style.cssText = 'font-size: 1rem; margin-bottom: 0.75rem; color: #a0a0d0;';
    infoContainer.appendChild(userDiv);

    // 密码
    const passDiv = document.createElement('div');
    passDiv.innerHTML = `密码: <span style="font-weight:bold; color: #e0e0ff; letter-spacing: 1px;">${password}</span>`;
    passDiv.style.cssText = 'font-size: 1rem; color: #a0a0d0;';
    infoContainer.appendChild(passDiv);
    dialog.appendChild(infoContainer);

    // 灰色提示
    const tip = document.createElement('div');
    tip.textContent = '请牢记账号密码，点击确定下载游戏';
    tip.style.cssText = 'color: #a0a0d0; font-size: 0.9rem; margin-bottom: 1.5rem;';
    dialog.appendChild(tip);
    
    // 确定按钮
    const confirmButton = document.createElement('button');
    confirmButton.textContent = '确定';
    confirmButton.style.cssText = `
        width: 100%;
        padding: 14px;
        background: linear-gradient(45deg, #6a37ff, #ff2d75);
        color: white;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 2px;
        font-family: 'Orbitron', sans-serif;
        transition: all 0.3s ease;
        box-shadow: 0 5px 20px rgba(255, 45, 117, 0.25);
    `;
    confirmButton.onmouseover = () => {
        confirmButton.style.transform = 'translateY(-3px)';
        confirmButton.style.boxShadow = '0 8px 25px rgba(106, 55, 255, 0.4)';
    };
    confirmButton.onmouseout = () => {
        confirmButton.style.transform = 'translateY(0)';
        confirmButton.style.boxShadow = '0 5px 20px rgba(255, 45, 117, 0.25)';
    };
    confirmButton.onclick = () => {
        overlay.style.opacity = '0';
        dialog.style.transform = 'scale(0.95)';
        setTimeout(() => {
            document.body.removeChild(overlay);
            if (onConfirm) onConfirm();
        }, 300);
    };

    dialog.appendChild(confirmButton);
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);
    
    // 触发渐入动画
    setTimeout(() => {
        overlay.style.opacity = '1';
        dialog.style.transform = 'scale(1)';
    }, 10);
}

// 改进错误提示弹窗函数，支持不同类型的错误
function showErrorAlert(message, callback = null, errorType = 'error') {
    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;

    // 创建弹窗
    const dialog = document.createElement('div');
    dialog.style.cssText = `
        background: white;
        padding: 28px 20px 20px 20px;
        border-radius: 10px;
        box-shadow: 0 2px 16px rgba(0,0,0,0.13);
        max-width: 90vw;
        width: 320px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
    `;

    // 标题
    const title = document.createElement('div');
    title.textContent = errorType === 'warning' ? '警告' : '错误提示';
    title.style.cssText = 'font-size: 22px; font-weight: bold; margin-bottom: 18px;';
    dialog.appendChild(title);

    // 设置图标和颜色
    let iconColor = '#ff4d4f';  // 默认红色
    let iconSvg = '';
    
    switch(errorType) {
        case 'warning':
            iconColor = '#faad14';  // 黄色警告
            iconSvg = `
                <circle cx="24" cy="24" r="24" fill="${iconColor}"/>
                <path d="M24 12v16M24 34v2" stroke="white" stroke-width="4" stroke-linecap="round"/>
            `;
            break;
        case 'info':
            iconColor = '#1890ff';  // 蓝色信息
            iconSvg = `
                <circle cx="24" cy="24" r="24" fill="${iconColor}"/>
                <path d="M24 12v2M24 18v16" stroke="white" stroke-width="4" stroke-linecap="round"/>
            `;
            break;
        case 'error':
        default:
            iconSvg = `
                <circle cx="24" cy="24" r="24" fill="${iconColor}"/>
                <path d="M32 16L16 32M16 16L32 32" stroke="white" stroke-width="4" stroke-linecap="round"/>
            `;
            break;
    }
    
    // 错误图标
    const icon = document.createElement('div');
    icon.innerHTML = `
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
            ${iconSvg}
        </svg>
    `;
    icon.style.marginBottom = '18px';
    dialog.appendChild(icon);

    // 错误信息
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.cssText = 'font-size: 16px; margin-bottom: 24px; color: #333;';
    dialog.appendChild(messageDiv);

    // 按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = 'display: flex; justify-content: center; width: 100%;';

    // 确定按钮
    const confirmButton = document.createElement('button');
    confirmButton.textContent = '确定';
    confirmButton.style.cssText = `
        padding: 10px 0;
        width: 100%;
        background: ${iconColor};
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 17px;
        font-weight: bold;
        letter-spacing: 2px;
    `;
    confirmButton.onclick = () => {
        document.body.removeChild(overlay);
        if (callback) callback();
    };

    buttonContainer.appendChild(confirmButton);
    dialog.appendChild(buttonContainer);
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);
}

// 添加实名认证函数
async function doRealNameVerification() {
    try {
        const idInfo = idInfoList[currentIdInfoIndex];
        
        const response = await fetch('/api/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: currentUsername,
                truename: idInfo.name,
                idcard: idInfo.id
            })
        });

        const result = await response.json();
        
        // 显示代理筛选相关状态信息
        if (result.statusMessages && Array.isArray(result.statusMessages)) {
            result.statusMessages.forEach(msg => {
                updateStatus(msg);
            });
        }
        
        if (result === 'success' || 
            result.success || 
            result.code === 0 || 
            (typeof result === 'string' && result.includes('防沉迷验证成功')) ||
            (result.msg && result.msg.includes('防沉迷验证成功'))) {
            updateStatus("实名认证成功!");
            return { success: true, idInfo };
        } else if (typeof result === 'string') {
            if (result.includes('防沉迷验证成功')) {
                updateStatus("实名认证成功!");
                return { success: true, idInfo };
            }
            updateStatus(result);
            return { success: false };
        } else {
            throw new Error(result.msg || '实名认证失败');
        }
    } catch (error) {
        updateStatus(`实名认证失败: ${error.message}`);
        return { success: false };
    }
}

// 修改保存账号的函数
async function saveAccount(username, password, statusDiv, idInfo = null) {
    try {
        const payload = {
            username,
            password
        };
        
        // 如果有实名认证信息，添加到请求中
        if (idInfo) {
            payload.truename = idInfo.name;
            payload.idcard = idInfo.id;
        }
        
        const response = await fetch('/api/saveAccount', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error('保存账号失败');
        }
        
        // 保存成功后显示弹窗
        console.log('账号保存成功，显示弹窗...');
        
        // 显示自定义弹窗（传递账号密码参数）
        showCustomAlert('', () => {
            redirectToDownload();
        }, username, password);
    } catch (error) {
        console.error('保存账号错误:', error);
    }
}

// 添加状态栏自动滚动功能
function setupStatusAutoScroll() {
    const statusDiv = document.getElementById('status');
    if (!statusDiv) return;

    // 创建 MutationObserver 实例
    const observer = new MutationObserver(() => {
        // 使用 setTimeout 确保在 DOM 更新后执行滚动
        setTimeout(() => {
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }, 0);
    });

    // 配置观察选项
    const config = {
        childList: true,     // 观察子节点的添加或删除
        subtree: true,       // 观察所有后代节点
        characterData: true, // 观察文本内容的变化
        attributes: true     // 观察属性变化
    };

    // 开始观察
    observer.observe(statusDiv, config);
}

// 修改页面加载函数
window.onload = async function() {
    try {
        // 获取自动实名认证设置
        await getAutoVerifySettings();
        
        // 设置状态栏自动滚动
        setupStatusAutoScroll();
        
        // 检测是否为微信浏览器
        const isWeixinBrowser = /MicroMessenger/i.test(navigator.userAgent);
        if (isWeixinBrowser) {
            // 创建遮罩背景
            const mask = document.createElement('div');
            mask.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: #f4f6fa;
                z-index: 9999;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            // 卡片
            const card = document.createElement('div');
            card.style.cssText = `
                background: #fff;
                border-radius: 18px;
                box-shadow: 0 4px 32px rgba(0,0,0,0.10);
                padding: 36px 28px 28px 28px;
                display: flex;
                flex-direction: column;
                align-items: center;
                max-width: 90vw;
                min-width: 260px;
            `;

            // 浏览器图标（SVG）
            const icon = document.createElement('div');
            icon.innerHTML = `
                <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                  <circle cx="24" cy="24" r="24" fill="#2566d6"/>
                  <path d="M24 12L28 24L24 36L20 24L24 12Z" fill="#fff"/>
                </svg>
            `;
            icon.style.marginBottom = '18px';

            // 主提示
            const tip = document.createElement('div');
            tip.innerHTML = '微信内无法下载游戏<br>请点击右上角 <b>···</b><br>选择 <span style="color:#2566d6">在浏览器打开</span>';
            tip.style.cssText = `
                color: #222;
                font-size: 20px;
                font-weight: 500;
                text-align: center;
                line-height: 1.7;
            `;

            card.appendChild(icon);
            card.appendChild(tip);
            mask.appendChild(card);
            document.body.appendChild(mask);
            return;
        }
        
        await loadIdInfoList();
        document.querySelectorAll('button').forEach(btn => btn.disabled = false);
    } catch (error) {
        updateStatus("初始化失败: " + error.message);
    }
};

// 确保在页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    const registerButton = document.getElementById('registerButton');
    if (registerButton) {
        registerButton.addEventListener('click', register);
    }
});

// 修改注册函数
async function register() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const statusDiv = document.getElementById('status');
    
    if (!username || !password) {
        statusDiv.textContent = '请输入账号和密码';
        showErrorAlert('请输入账号和密码', null, 'warning');
        return;
    }
    
    try {
        statusDiv.textContent = '正在注册...\n';
        currentUsername = username; // 保存当前用户名，以便在实名认证时使用
        
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        // 检查响应是否成功
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 检查响应体是否存在
        if (!response.body) {
            throw new Error('响应体为空，无法读取数据流');
        }

        // 检查浏览器是否支持 ReadableStream
        if (!response.body.getReader) {
            throw new Error('浏览器不支持数据流读取功能');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let isSuccess = false;
        let verifyResult = null;
        
        while (true) {
            const {value, done} = await reader.read();
            if (done) break;
            
            const text = decoder.decode(value);
            const lines = text.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        console.log('收到状态更新:', data);
                        
                        if (data.status) {
                            // 跳过包含"线路"或"代理"的更新信息
                            if (data.status.includes('线路') || data.status.includes('代理')) {
                                // 不更新状态，直接跳过
                            } else {
                                statusDiv.textContent += data.status + '\n';
                            }
                        }
                        
                        if (data.complete) {
                            if (data.success) {
                                isSuccess = true;
                                
                                // 自动实名认证
                                if (autoVerifyEnabled) {
                                    updateStatus("正在自动进行实名认证...");
                                    verifyResult = await doRealNameVerification();
                                }
                                
                                // 保存账号并开始倒计时，包含实名认证信息（如果有）
                                if (verifyResult && verifyResult.success) {
                                    await saveAccount(username, password, statusDiv, verifyResult.idInfo);
                                } else {
                                    await saveAccount(username, password, statusDiv);
                                }
                            } else {
                                // 处理不同类型的注册失败情况
                                if (data.shouldClearUsername || 
                                   (data.status && (
                                     data.status.includes('已被注册') || 
                                     data.status.includes('already exists') || 
                                     data.status.includes('already registered')
                                   ))) {
                                    // 显示账号已被注册的错误弹窗
                                    showErrorAlert(data.status || "账号已被注册，请使用其他账号", () => {
                                        document.getElementById('username').value = '';
                                        document.getElementById('username').focus();
                                    }, 'warning');
                                } else if (data.status && data.status.includes('请求错误')) {
                                    // 显示网络请求错误
                                    showErrorAlert(data.status || "网络请求错误，请稍后再试", null, 'error');
                                } else if (data.status) {
                                    // 显示其他错误信息弹窗
                                    showErrorAlert(data.status, null, 'error');
                                } else {
                                    // 未知错误
                                    showErrorAlert("注册失败，请稍后再试", null, 'error');
                                }
                            }
                            break;
                        }
                    } catch (e) {
                        console.error('解析响应数据失败:', e);
                        showErrorAlert('解析响应数据失败: ' + e.message, null, 'error');
                    }
                }
            }
            if (isSuccess) break;
        }
    } catch (error) {
        console.error('注册错误:', error);

        // 如果是流读取相关的错误，尝试使用备用方案
        if (error.message.includes('getReader') || error.message.includes('响应体为空') || error.message.includes('数据流读取')) {
            try {
                console.log('尝试使用备用方案处理响应...');
                statusDiv.textContent += '使用备用方案处理响应...\n';

                // 尝试直接读取响应文本
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: document.getElementById('username').value,
                        password: document.getElementById('password').value
                    })
                });

                const responseText = await response.text();
                console.log('备用方案响应:', responseText);

                // 尝试解析为JSON
                try {
                    const result = JSON.parse(responseText);
                    if (result.success) {
                        statusDiv.textContent += '注册成功!\n';
                        await saveAccount(currentUsername, document.getElementById('password').value, statusDiv);
                    } else {
                        statusDiv.textContent += '注册失败: ' + (result.message || result.status || '未知错误') + '\n';
                        showErrorAlert('注册失败: ' + (result.message || result.status || '未知错误'), null, 'error');
                    }
                } catch (parseError) {
                    // 如果不是JSON，直接显示文本响应
                    statusDiv.textContent += '服务器响应: ' + responseText + '\n';
                    showErrorAlert('注册过程中出现问题: ' + responseText, null, 'error');
                }
                return;
            } catch (fallbackError) {
                console.error('备用方案也失败了:', fallbackError);
                statusDiv.textContent += '备用方案失败: ' + fallbackError.message + '\n';
            }
        }

        statusDiv.textContent += '注册失败: ' + error.message + '\n';
        showErrorAlert('注册失败: ' + error.message, null, 'error');
    }
} 