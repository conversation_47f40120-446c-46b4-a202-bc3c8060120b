<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奇迹账号注册 - </title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700&family=Exo+2:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --space-blue: #0c1220;
            --neon-purple: #6a37ff;
            --electric-blue: #00d2ff;
            --plasma-pink: #ff2d75;
            --glass-bg: rgba(25, 28, 50, 0.4);
            --glass-border: rgba(255, 255, 255, 0.15);
            --text-primary: #e0e0ff;
            --text-secondary: #a0a0d0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Exo 2', sans-serif;
            background: var(--space-blue);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }

        #particles-js {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
        }

        .container {
            position: relative;
            z-index: 10;
            width: 90%;
            max-width: 460px;
            padding: 2rem 0;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            animation: fadeInDown 0.8s ease-out;
        }

        .logo {
            font-family: 'Orbitron', sans-serif;
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--electric-blue), var(--neon-purple));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 25px rgba(106, 55, 255, 0.4);
        }

        .subtitle {
            font-size: 1rem;
            color: var(--text-secondary);
            font-weight: 300;
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: 20px;
            padding: 2.5rem;
            border: 1px solid var(--glass-border);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: fadeIn 1s ease-out forwards;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 500;
            color: var(--electric-blue);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        .input-container {
            position: relative;
        }

        .input-container .icon {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1.1rem;
            transition: color 0.3s ease;
            width: 45px;
            text-align: center;
        }

        .form-control {
            width: 100%;
            padding: 14px 16px 14px 45px;
            background: rgba(12, 18, 32, 0.6);
            border: 1px solid transparent;
            border-bottom: 1px solid var(--glass-border);
            border-radius: 10px;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
            outline: none;
            font-family: 'Exo 2', sans-serif;
        }

        .form-control:focus {
            border-color: var(--electric-blue);
            background: rgba(20, 30, 50, 0.7);
            box-shadow: 0 0 15px rgba(0, 210, 255, 0.2);
        }
        
        .form-control:focus + .icon {
            color: var(--electric-blue);
        }

        .form-control::placeholder {
            color: rgba(160, 160, 208, 0.5);
        }

        .btn-register {
            width: 100%;
            padding: 16px;
            background: linear-gradient(45deg, var(--neon-purple), var(--plasma-pink));
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: 2px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', sans-serif;
            position: relative;
            box-shadow: 0 5px 20px rgba(255, 45, 117, 0.25);
        }

        .btn-register:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(106, 55, 255, 0.4);
        }

        .divider {
            height: 1px;
            background: var(--glass-border);
            margin: 2rem 0;
            opacity: 0.7;
        }

        .status-header {
            display: flex;
            align-items: center;
            font-weight: 500;
            color: var(--electric-blue);
            margin-bottom: 1rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        .status-header i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        #status {
            height: 100px;
            overflow-y: auto;
            background: rgba(12, 18, 32, 0.6);
            border-radius: 8px;
            padding: 12px;
            font-size: 0.9rem;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
            border: 1px solid var(--glass-border);
            scrollbar-width: thin;
            scrollbar-color: var(--electric-blue) transparent;
        }

        #status::-webkit-scrollbar { width: 5px; }
        #status::-webkit-scrollbar-track { background: transparent; }
        #status::-webkit-scrollbar-thumb { background-color: var(--electric-blue); border-radius: 5px; }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div id="particles-js"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="logo">奇迹MU</h1>
            <p class="subtitle">账号注册</p>
        </div>
        
        <div class="card">
            <form id="registerForm" onsubmit="event.preventDefault(); register();">
                <div class="form-group">
                    <label for="username">游戏账号</label>
                    <div class="input-container">
                        <input type="text" id="username" class="form-control" placeholder="请输入您的账号" autocomplete="off" required>
                        <i class="fas fa-user-astronaut icon"></i>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">游戏密码</label>
                    <div class="input-container">
                        <input type="text" id="password" class="form-control" placeholder="请输入您的密码" autocomplete="off" required>
                        <i class="fas fa-lock icon"></i>
                    </div>
                </div>
                
                <button type="submit" class="btn-register">
                    <i class="fas fa-rocket"></i> 立即注册
                </button>
            </form>

            <div class="divider"></div>

            <div class="status-container">
                <div class="status-header">
                    <i class="fas fa-satellite-dish"></i>
                    <span>注册状态</span>
                </div>
                <div id="status"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            particlesJS('particles-js', { /* particles.js config */
                "particles": {
                    "number": { "value": 80, "density": { "enable": true, "value_area": 800 } },
                    "color": { "value": "#ffffff" },
                    "shape": { "type": "circle" },
                    "opacity": { "value": 0.5, "random": true, "anim": { "enable": true, "speed": 1, "opacity_min": 0.1, "sync": false } },
                    "size": { "value": 2, "random": true, "anim": { "enable": false } },
                    "line_linked": { "enable": true, "distance": 150, "color": "#ffffff", "opacity": 0.1, "width": 1 },
                    "move": { "enable": true, "speed": 1, "direction": "none", "random": true, "straight": false, "out_mode": "out" }
                },
                "interactivity": {
                    "detect_on": "canvas",
                    "events": { "onhover": { "enable": true, "mode": "grab" }, "onclick": { "enable": true, "mode": "push" } },
                    "modes": { "grab": { "distance": 140, "line_linked": { "opacity": 0.2 } }, "push": { "particles_nb": 4 } }
                },
                "retina_detect": true
            });
        });

        let proxyUpdateInterval = null;
        function startProxyPoolUpdate() {
            if (proxyUpdateInterval) return;
            updateProxyPool();
            proxyUpdateInterval = setInterval(updateProxyPool, 5000);
        }
        function updateProxyPool() { return fetch('/api/updateProxyPool').catch(console.error); }
        function getProxy() { return fetch('/api/getProxy').catch(console.error); }
    </script>
    <script src="script.js"></script>
</body>
</html> 