:root {
    --primary-color: #4f8cff;
    --primary-light: #6ed0ff;
    --text-color: #2c3e50;
    --text-light: #666;
    --background: #f8f9ff;
    --white: #ffffff;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --border-radius: 16px;
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(120deg, var(--primary-color) 0%, var(--primary-light) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
}

.container {
    width: min(92%, 420px);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: clamp(25px, 6vw, 35px);
    border-radius: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    position: relative;
    animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

h1 {
    text-align: center;
    color: #1a1a1a;
    margin-bottom: clamp(25px, 6vw, 35px);
    font-size: clamp(24px, 6vw, 32px);
    font-weight: 700;
    letter-spacing: -0.5px;
}

.input-group {
    margin-bottom: clamp(20px, 5vw, 30px);
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    color: #444;
    font-weight: 500;
    font-size: clamp(14px, 4vw, 16px);
    transition: color 0.3s;
}

.input-group input {
    width: 100%;
    height: 50px;
    padding: 0 16px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    background: #fff;
    transition: all 0.3s;
    box-sizing: border-box;
}

.input-group input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* 修改代理组的样式为隐藏 */
.proxy-group {
    margin: clamp(15px, 4vw, 25px) 0;
    padding: clamp(12px, 3vw, 20px);
    background-color: rgba(248, 248, 248, 0.9);
    border-radius: clamp(6px, 2vw, 8px);
    display: flex;
    align-items: center;
    gap: clamp(8px, 2vw, 12px);
    flex-wrap: wrap;
    font-size: clamp(14px, 4vw, 16px);
}

/* 移除子元素的隐藏样式 */
.proxy-group input,
.proxy-group label,
.proxy-group button,
.proxy-group span {
    display: initial;
}

.button-group {
    display: flex;
    gap: 12px;
    margin-top: 30px;
}

.button-group button {
    flex: 1;
    height: 50px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    background: #4a90e2;
    color: white;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.button-group button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(74, 144, 226, 0.4);
}

.button-group button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.status-container {
    margin-top: 25px;
    padding: 16px;
    background: #f8f9ff;
    border-radius: 12px;
    max-height: 150px;
    overflow-y: auto;
}

.status-container h3 {
    margin: 0 0 10px 0;
    color: #444;
    font-size: 16px;
    font-weight: 600;
}

.status-text {
    white-space: pre-wrap;
    font-family: 'SF Mono', Consolas, Monaco, monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #666;
}

::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@media screen and (max-width: 480px) {
    .container {
        width: 94%;
        padding: 20px;
        margin: 15px;
        border-radius: 20px;
    }
    
    .button-group {
        flex-direction: row;
        gap: 10px;
    }
    
    .button-group button {
        height: 44px;
        font-size: 15px;
        padding: 0 10px;
    }
}

/* 移动端优化 */
@media screen and (max-width: 480px) {
    .container {
        width: 92%;
        margin: 15px auto;
    }
    
    .proxy-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .proxy-group button {
        width: 100%;
        height: 40px;
        margin-top: 10px;
    }
    
    #proxy-status {
        width: 100%;
        margin-top: 10px;
        word-break: break-all;
    }
}

/* 优化系统弹窗 */
@media screen and (max-width: 480px) {
    .swal2-popup {
        font-size: 16px !important;
        width: 90% !important;
        padding: 15px !important;
    }
    
    .swal2-title {
        font-size: 20px !important;
    }
    
    .swal2-content {
        font-size: 16px !important;
    }
    
    .swal2-actions button {
        font-size: 16px !important;
        padding: 8px 20px !important;
    }
}

/* 添加媒体查询优化移动端样式 */
@media screen and (max-width: 480px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    .proxy-group {
        font-size: 12px;
    }
    
    .proxy-group button {
        padding: 8px 15px;
        font-size: 12px;
    }
    
    #proxy-status {
        width: 100%;
        margin-top: 5px;
    }
}

/* 优化系统弹窗样式 */
@media screen and (max-width: 480px) {
    /* 通过 CSS 变量设置系统弹窗样式 */
    :root {
        --native-dialog-width: 90%;
        --native-dialog-max-width: 280px;
        --native-dialog-padding: 15px;
        --native-dialog-font-size: 14px;
        --native-dialog-button-height: 36px;
    }
}

/* 修改代理状态样式 */
#proxy-status {
    font-family: monospace;
    padding: 4px 8px;
    border-radius: 4px;
    background: #f5f5f5;
    color: #666;
    font-size: 13px;
    word-break: break-all;
}

/* 添加状态颜色 */
#proxy-status.connected {
    color: #2ecc71;
    background: #eafaf1;
}

#proxy-status.connecting {
    color: #f39c12;
    background: #fef9e7;
}

#proxy-status.disconnected {
    color: #e74c3c;
    background: #fdedec;
}

.register-container {
    width: min(92%, 420px);
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    position: relative;
    animation: slideUp 0.4s ease-out;
}

.register-header {
    text-align: center;
    margin-bottom: 2rem;
}

.register-header i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.register-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.register-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
}

.input-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 500;
}

.input-group label i {
    color: var(--primary-color);
}

.input-group input {
    width: 100%;
    height: 3rem;
    padding: 0 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 0.75rem;
    font-size: 1rem;
    background: var(--white);
    transition: var(--transition);
    box-sizing: border-box;
}

.input-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(79, 140, 255, 0.1);
    outline: none;
}

.register-btn {
    height: 3rem;
    border: none;
    border-radius: 0.75rem;
    background: var(--primary-color);
    color: var(--white);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.register-btn:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 140, 255, 0.2);
}

.register-btn:active {
    transform: translateY(0);
}

.status-container {
    margin-top: 2rem;
    padding: 1rem;
    background: var(--background);
    border-radius: 0.75rem;
    max-height: 150px;
    overflow-y: auto;
}

.status-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.status-header i {
    color: var(--primary-color);
}

.status-label {
    font-weight: 600;
    color: var(--text-color);
}

.status-text {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-light);
    white-space: pre-wrap;
}

@media screen and (max-width: 480px) {
    .register-container {
        width: 94%;
        padding: 1.2rem 1.2rem;
        margin: 2rem auto;
        box-sizing: border-box;
    }
    .register-title {
        font-size: 1.25rem;
    }
    .input-group input {
        height: 2.75rem;
    }
    .register-btn {
        height: 2.75rem;
    }
} 