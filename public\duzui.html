<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .header {
            background-color: #fff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 20px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logout-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .logout-btn:hover {
            background-color: #c82333;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background-color: #fff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stat-card p {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .accounts-list {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .account-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .account-table th {
            background-color: #f8f9fa;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            color: #666;
            border-bottom: 1px solid #eee;
        }
        
        .sortable {
            cursor: pointer;
            position: relative;
        }
        
        .sortable:hover {
            background-color: #eef2f7;
        }
        
        .sortable::after {
            content: "↕️";
            font-size: 10px;
            margin-left: 5px;
            opacity: 0.5;
        }
        
        .sort-asc::after {
            content: "↑";
            opacity: 1;
        }
        
        .sort-desc::after {
            content: "↓";
            opacity: 1;
        }
        
        .account-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #eee;
            font-size: 13px;
            word-break: break-all;
        }
        
        .account-table tr:last-child td {
            border-bottom: none;
        }
        
        .account-table tr.verified {
            background-color: #edf7ed;
        }
        
        .account-time {
            font-size: 12px;
            color: #999;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #2566d6;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer;
        }
        
        .settings-card {
            background-color: #fff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .settings-card h2 {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-label {
            font-size: 14px;
            color: #333;
        }
        
        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #2566d6;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .status-message {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
            display: none;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            display: block;
        }
        
        .query-btn {
            background-color: #2566d6;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .query-btn:hover {
            background-color: #1e4cad;
        }
        
        .money-value {
            margin-left: 5px;
            font-weight: 500;
        }
        
        .button-container {
            margin-bottom: 20px;
            display: flex;
            justify-content: flex-end;
        }
        
        .action-btn {
            background-color: #2566d6;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .action-btn:hover {
            background-color: #1e4cad;
        }
        
        .action-btn:disabled {
            background-color: #7fa4e7;
            cursor: not-allowed;
        }
        
        .danger-btn {
            background-color: #dc3545;
        }
        
        .danger-btn:hover {
            background-color: #c82333;
        }
        
        .danger-btn:disabled {
            background-color: #e99ca3;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 500px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .modal-body {
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
        }
        
        .close-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            cursor: pointer;
        }
        
        .deleted-item {
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #dc3545;
        }
        
        @media (min-width: 768px) {
            .container {
                max-width: 1200px;
            }
            
            .stats {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        /* 登录验证层样式 */
        .login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .login-card {
            width: 320px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.2);
            padding: 30px;
        }
        
        .login-card h2 {
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .login-form input {
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .login-form button {
            width: 100%;
            padding: 12px;
            background-color: #2566d6;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .login-form button:hover {
            background-color: #1e4cad;
        }
        
        .login-error {
            color: #dc3545;
            font-size: 14px;
            text-align: center;
            margin-top: 10px;
            display: none;
        }
        
        .hidden {
            display: none;
        }
        
    </style>
</head>
<body>
    <!-- 登录验证层 -->
    <div class="login-overlay" id="loginOverlay">
        <div class="login-card">
            <h2>管理后台登录</h2>
            <form class="login-form" id="loginForm">
                <input type="text" id="username" placeholder="用户名" required>
                <input type="password" id="password" placeholder="密码" required>
                <button type="submit">登录</button>
                <p class="login-error" id="loginError">用户名或密码错误</p>
            </form>
        </div>
    </div>

         <!-- 主界面内容，初始隐藏 -->
     <div class="container hidden" id="mainContent">
        <div class="header">
            <div class="header-content">
                <div>
                    <h1>账号管理后台</h1>
                    <p>实时监控注册账号情况</p>
                </div>
                <button class="logout-btn" id="logoutBtn">退出登录</button>
            </div>
        </div>
        
        <div class="settings-card">
            <h2>系统设置</h2>
            <div class="setting-item">
                <span class="setting-label">自动实名认证</span>
                <label class="switch">
                    <input type="checkbox" id="autoVerifySwitch">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="setting-item">
                <span class="setting-label">使用代理注册账号</span>
                <label class="switch">
                    <input type="checkbox" id="useProxyRegisterSwitch">
                    <span class="slider"></span>
                </label>
            </div>
            <div class="setting-item">
                <span class="setting-label">使用代理查询充值</span>
                <label class="switch">
                    <input type="checkbox" id="useProxySwitch">
                    <span class="slider"></span>
                </label>
            </div>
            <div id="settingStatus" class="status-message"></div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>今日注册</h3>
                <p id="todayCount">0</p>
            </div>
            <div class="stat-card">
                <h3>总注册数</h3>
                <p id="totalCount">0</p>
            </div>
            <div class="stat-card">
                <h3>已实名认证</h3>
                <p id="verifiedCount">0</p>
            </div>
        </div>
        
        <div class="button-container">
            <button id="queryAllBtn" class="action-btn">查询所有充值</button>
            <button id="deleteInactiveBtn" class="action-btn danger-btn">清理过期账号</button>
            <button id="deleteUnusedBtn" class="action-btn danger-btn">清理未使用账号</button>
        </div>
        
        <div class="accounts-list">
            <table class="account-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>账号</th>
                        <th>密码</th>
                        <th>角色名</th>
                        <th class="sortable" id="moneyHeader">充值金额</th>
                        <th class="sortable" id="loginHeader">最后登录</th>
                        <th>名字</th>
                        <th>身份证</th>
                    </tr>
                </thead>
                <tbody id="accountsList">
                    <!-- 账号列表将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="loadAccounts()">刷新数据</button>
    </div>
    
    <script>
        // 验证登录的函数
        function validateLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginError = document.getElementById('loginError');
            
            // 验证账号和密码
            if (username === 'duzui' && password === '786946') {
                // 登录成功，显示主界面
                document.getElementById('loginOverlay').classList.add('hidden');
                document.getElementById('mainContent').classList.remove('hidden');
                
                // 保存登录状态到会话存储
                sessionStorage.setItem('duizuiLoggedIn', 'true');
                
                // 加载账号数据
                loadAccounts();
                getAutoVerifyStatus();
                getProxyRegisterStatus();
                getProxyStatus();
            } else {
                // 显示错误信息
                loginError.style.display = 'block';
            }
        }
        
        // 绑定登录表单提交事件
        document.getElementById('loginForm').addEventListener('submit', validateLogin);
        
        // 检查是否已经登录
        function checkLoginStatus() {
            if (sessionStorage.getItem('duizuiLoggedIn') === 'true') {
                // 如果已登录，直接显示主界面
                document.getElementById('loginOverlay').classList.add('hidden');
                document.getElementById('mainContent').classList.remove('hidden');
                
                // 加载账号数据
                loadAccounts();
                getAutoVerifyStatus();
                getProxyRegisterStatus();
                getProxyStatus();
            }
        }
        
                 // 页面加载时检查登录状态
         window.addEventListener('DOMContentLoaded', checkLoginStatus);
         
         // 退出登录功能
         function logout() {
             // 清除登录状态
             sessionStorage.removeItem('duizuiLoggedIn');
             // 显示登录界面，隐藏主界面
             document.getElementById('loginOverlay').classList.remove('hidden');
             document.getElementById('mainContent').classList.add('hidden');
             // 清空登录表单
             document.getElementById('username').value = '';
             document.getElementById('password').value = '';
             document.getElementById('loginError').style.display = 'none';
         }
         
         // 绑定退出按钮事件
         document.getElementById('logoutBtn').addEventListener('click', logout);
                 
        // 获取自动实名认证设置状态
        async function getAutoVerifyStatus() {
            try {
                const response = await fetch('/api/settings/autoVerify');
                const data = await response.json();
                document.getElementById('autoVerifySwitch').checked = data.enabled;
            } catch (error) {
                console.error('获取设置失败:', error);
            }
        }
        
        // 获取代理注册设置状态
        async function getProxyRegisterStatus() {
            try {
                const response = await fetch('/api/settings/useProxyRegister');
                const data = await response.json();
                document.getElementById('useProxyRegisterSwitch').checked = data.enabled;
            } catch (error) {
                console.error('获取代理注册设置失败:', error);
                // 默认开启
                document.getElementById('useProxyRegisterSwitch').checked = true;
            }
        }

        // 获取代理设置状态
        async function getProxyStatus() {
            try {
                const response = await fetch('/api/settings/useProxy');
                const data = await response.json();
                document.getElementById('useProxySwitch').checked = data.enabled;
            } catch (error) {
                console.error('获取代理设置失败:', error);
                // 默认关闭
                document.getElementById('useProxySwitch').checked = false;
            }
        }
        
        // 保存自动实名认证设置
        async function saveAutoVerifyStatus(enabled) {
            try {
                const response = await fetch('/api/settings/autoVerify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled })
                });
                
                const data = await response.json();
                
                const statusEl = document.getElementById('settingStatus');
                statusEl.textContent = data.success ? '设置已保存' : '保存设置失败';
                statusEl.className = data.success ? 'status-message success' : 'status-message error';
                
                // 3秒后隐藏状态消息
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);
                
                return data.success;
            } catch (error) {
                console.error('保存设置失败:', error);
                return false;
            }
        }
        
        // 保存代理注册设置状态
        async function saveProxyRegisterStatus(enabled) {
            try {
                const response = await fetch('/api/settings/useProxyRegister', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled })
                });

                const data = await response.json();

                const statusEl = document.getElementById('settingStatus');
                statusEl.textContent = data.success ? '代理注册设置已保存' : '保存代理注册设置失败';
                statusEl.className = data.success ? 'status-message success' : 'status-message error';

                // 3秒后隐藏状态消息
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);

                return data.success;
            } catch (error) {
                console.error('保存代理注册设置失败:', error);
                return false;
            }
        }

        // 保存代理设置状态
        async function saveProxyStatus(enabled) {
            try {
                const response = await fetch('/api/settings/useProxy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled })
                });

                const data = await response.json();

                const statusEl = document.getElementById('settingStatus');
                statusEl.textContent = data.success ? '代理设置已保存' : '保存代理设置失败';
                statusEl.className = data.success ? 'status-message success' : 'status-message error';

                // 3秒后隐藏状态消息
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);

                return data.success;
            } catch (error) {
                console.error('保存代理设置失败:', error);
                return false;
            }
        }
        
        // 绑定开关事件
        document.getElementById('autoVerifySwitch').addEventListener('change', function(e) {
            saveAutoVerifyStatus(e.target.checked);
        });

        // 绑定代理注册开关事件
        document.getElementById('useProxyRegisterSwitch').addEventListener('change', function(e) {
            saveProxyRegisterStatus(e.target.checked);
        });

        // 绑定代理开关事件
        document.getElementById('useProxySwitch').addEventListener('change', function(e) {
            saveProxyStatus(e.target.checked);
        });
        
        // 解析账号字符串，提取关键信息
        function parseAccountInfo(accountStr) {
            const parts = accountStr.split(' - ');
            const timestamp = parts[0];
            const infoStr = parts[1] || '';
            
            // 解析账号信息
            const accountMatch = infoStr.match(/账号：(\S+)/);
            const passwordMatch = infoStr.match(/密码：(\S+)/);
            const nameMatch = infoStr.match(/名字：(\S+)/);
            const idcardMatch = infoStr.match(/身份证：(\S+)/);
            
            return {
                timestamp,
                account: accountMatch ? accountMatch[1] : '',
                password: passwordMatch ? passwordMatch[1] : '',
                name: nameMatch ? nameMatch[1] : '',
                idcard: idcardMatch ? idcardMatch[1] : '',
                isVerified: !!(nameMatch && idcardMatch),
                money: null // 初始充值金额为null，表示未查询
            };
        }
        
        // 查询所有账号
        async function queryAllAccounts() {
            const moneyElements = document.querySelectorAll('td.account-money');
            const button = document.getElementById('queryAllBtn');
            
            // 更改按钮文本且禁用
            const originalText = button.textContent;
            button.textContent = "查询中...";
            button.disabled = true;
            
            try {
                // 获取所有需要查询的账号信息
                const accountRows = document.querySelectorAll('#accountsList tr');
                const queryTasks = [];
                
                accountRows.forEach(row => {
                    const accountCell = row.cells[1]; // 账号单元格
                    const moneyCell = row.querySelector('.account-money');
                    const roleNameCell = row.querySelector('.role-name');
                    const lastLoginCell = row.querySelector('.last-login');
                    const timestamp = row.querySelector('.account-time')?.textContent;
                    
                    if (accountCell && moneyCell && roleNameCell && lastLoginCell) {
                        const account = accountCell.textContent;
                        
                        if (account) {
                            // 显示查询中状态
                            moneyCell.textContent = "查询中...";
                            roleNameCell.textContent = "查询中...";
                            lastLoginCell.textContent = "查询中...";
                            
                            // 创建查询充值金额的任务
                            const moneyTask = fetch('/api/queryPayment', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ username: account, timestamp })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // 记录原始数据
                                    console.log(`充值查询原始数据 (${account}):`, data);
                                    
                                    const moneyValue = parseFloat(data.money) || 0;
                                    moneyCell.textContent = moneyValue > 0 ? `¥${moneyValue}` : '¥0';
                                } else {
                                    console.error('查询充值金额失败:', data.error);
                                    moneyCell.textContent = '查询失败';
                                }
                            })
                            .catch(error => {
                                console.error(`查询账号 ${account} 充值金额失败:`, error);
                                moneyCell.textContent = '查询失败';
                            });
                            
                            // 创建查询登录信息的任务
                            const loginTask = fetch('/api/queryLoginInfo', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ username: account, timestamp })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // 记录原始数据
                                    console.log(`登录查询原始数据 (${account}):`, data);
                                    
                                    roleNameCell.textContent = data.roleName || '无角色';
                                    lastLoginCell.textContent = data.lastLoginTime || '未登录';
                                } else {
                                    console.error('查询登录信息失败:', data.error);
                                    roleNameCell.textContent = '查询失败';
                                    lastLoginCell.textContent = '查询失败';
                                }
                            })
                            .catch(error => {
                                console.error(`查询账号 ${account} 登录信息失败:`, error);
                                roleNameCell.textContent = '查询失败';
                                lastLoginCell.textContent = '查询失败';
                            });
                            
                            // 添加到任务列表
                            queryTasks.push(moneyTask, loginTask);
                        }
                    }
                });
                
                // 并行执行所有查询任务
                await Promise.all(queryTasks);
            } catch (error) {
                console.error('查询信息时发生错误:', error);
            } finally {
                // 恢复按钮
                button.textContent = originalText;
                button.disabled = false;
            }
        }
        
        // 删除过期账号
        async function deleteInactiveAccounts() {
            if (!confirm('确定要删除所有注册超过15天且无充值记录的账号吗？此操作不可撤销。')) {
                return;
            }
            
            const button = document.getElementById('deleteInactiveBtn');
            
            // 更改按钮文本且禁用
            const originalText = button.textContent;
            button.textContent = "处理中...";
            button.disabled = true;
            
            try {
                // 筛选需要删除的账号
                const accountsToDelete = [];
                const accountRows = document.querySelectorAll('#accountsList tr');
                const currentDate = new Date();
                
                accountRows.forEach(row => {
                    const timeCell = row.querySelector('.account-time');
                    const moneyCell = row.querySelector('.account-money');
                    const accountCell = row.cells[1]; // 账号单元格
                    
                    if (timeCell && moneyCell && accountCell) {
                        // 获取注册时间
                        const timestamp = timeCell.textContent;
                        const registerDate = new Date(timestamp.replace(/(\d{4})-(\d{2})-(\d{2}).*/, '$1/$2/$3'));
                        
                        // 计算天数差
                        const daysDiff = Math.floor((currentDate - registerDate) / (1000 * 60 * 60 * 24));
                        
                        // 获取充值金额
                        const moneyText = moneyCell.textContent;
                        const isZeroMoney = moneyText === '¥0' || moneyText === '-';
                        
                        // 如果超过15天且充值金额为0，加入删除列表
                        if (daysDiff > 15 && isZeroMoney) {
                            const accountName = accountCell.textContent;
                            accountsToDelete.push(accountName);
                            
                            // 标记行为待删除状态
                            row.style.backgroundColor = 'rgba(255, 200, 200, 0.3)';
                        }
                    }
                });
                
                if (accountsToDelete.length === 0) {
                    alert('没有找到符合条件的账号（注册超过15天且无充值记录）');
                    button.textContent = originalText;
                    button.disabled = false;
                    return;
                }
                
                // 发送删除请求
                const response = await fetch('/api/deleteInactiveAccounts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ accountsToDelete })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 创建结果展示对话框
                    const modal = document.createElement('div');
                    modal.className = 'modal';
                    
                    let deletedContent = '';
                    if (data.deleted > 0) {
                        deletedContent = data.deletedAccounts.map(account => `
                            <div class="deleted-item">
                                <div>账号: ${account.username}</div>
                                <div>注册时间: ${account.timestamp}</div>
                                <div>未充值天数: ${account.daysDiff}天</div>
                            </div>
                        `).join('');
                    } else {
                        deletedContent = '<p>没有符合条件的账号需要删除。</p>';
                    }
                    
                    modal.innerHTML = `
                        <div class="modal-content">
                            <div class="modal-title">清理结果</div>
                            <div class="modal-body">
                                <p>共删除 ${data.deleted} 个账号，保留 ${data.kept} 个账号。</p>
                                <hr>
                                <h4>删除的账号:</h4>
                                ${deletedContent}
                            </div>
                            <div class="modal-footer">
                                <button class="close-btn">关闭</button>
                            </div>
                        </div>
                    `;
                    
                    document.body.appendChild(modal);
                    modal.style.display = 'block';
                    
                    // 绑定关闭按钮事件
                    modal.querySelector('.close-btn').addEventListener('click', () => {
                        modal.style.display = 'none';
                        document.body.removeChild(modal);
                        // 刷新账号列表
                        loadAccounts();
                    });
                } else {
                    alert(`删除过期账号失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('删除过期账号请求失败:', error);
                alert('删除过期账号请求失败，请查看控制台了解详情。');
            } finally {
                // 恢复按钮
                button.textContent = originalText;
                button.disabled = false;
            }
        }
        
        // 删除未使用账号（注册超过3天未登录无角色）
        async function deleteUnusedAccounts() {
            if (!confirm('确定要删除所有注册超过3天且未登录无角色的账号吗？此操作不可撤销。')) {
                return;
            }
            
            const button = document.getElementById('deleteUnusedBtn');
            
            // 更改按钮文本且禁用
            const originalText = button.textContent;
            button.textContent = "处理中...";
            button.disabled = true;
            
            try {
                // 筛选需要删除的账号
                const accountsToDelete = [];
                const accountRows = document.querySelectorAll('#accountsList tr');
                const currentDate = new Date();
                
                accountRows.forEach(row => {
                    const timeCell = row.querySelector('.account-time');
                    const roleNameCell = row.querySelector('.role-name');
                    const lastLoginCell = row.querySelector('.last-login');
                    const accountCell = row.cells[1]; // 账号单元格
                    
                    if (timeCell && roleNameCell && lastLoginCell && accountCell) {
                        // 获取注册时间
                        const timestamp = timeCell.textContent;
                        const registerDate = new Date(timestamp.replace(/(\d{4})-(\d{2})-(\d{2}).*/, '$1/$2/$3'));
                        
                        // 计算天数差
                        const daysDiff = Math.floor((currentDate - registerDate) / (1000 * 60 * 60 * 24));
                        
                        // 检查是否无角色且未登录
                        const hasNoRole = roleNameCell.textContent === '无角色';
                        const hasNoLogin = lastLoginCell.textContent === '未登录';
                        
                        // 如果超过3天且无角色且未登录，加入删除列表
                        if (daysDiff > 3 && hasNoRole && hasNoLogin) {
                            const accountName = accountCell.textContent;
                            accountsToDelete.push(accountName);
                            
                            // 标记行为待删除状态
                            row.style.backgroundColor = 'rgba(255, 200, 200, 0.3)';
                        }
                    }
                });
                
                if (accountsToDelete.length === 0) {
                    alert('没有找到符合条件的账号（注册超过3天且未登录无角色）');
                    button.textContent = originalText;
                    button.disabled = false;
                    return;
                }
                
                // 发送删除请求
                const response = await fetch('/api/deleteUnusedAccounts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ accountsToDelete })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 创建结果展示对话框
                    const modal = document.createElement('div');
                    modal.className = 'modal';
                    
                    let deletedContent = '';
                    if (data.deleted > 0) {
                        deletedContent = data.deletedAccounts.map(account => `
                            <div class="deleted-item">
                                <div>账号: ${account.username}</div>
                                <div>注册时间: ${account.timestamp}</div>
                                <div>未使用天数: ${account.daysDiff}天</div>
                            </div>
                        `).join('');
                    } else {
                        deletedContent = '<p>没有符合条件的账号需要删除。</p>';
                    }
                    
                    modal.innerHTML = `
                        <div class="modal-content">
                            <div class="modal-title">清理结果</div>
                            <div class="modal-body">
                                <p>共删除 ${data.deleted} 个未使用账号，保留 ${data.kept} 个账号。</p>
                                <hr>
                                <h4>删除的账号:</h4>
                                ${deletedContent}
                            </div>
                            <div class="modal-footer">
                                <button class="close-btn">关闭</button>
                            </div>
                        </div>
                    `;
                    
                    document.body.appendChild(modal);
                    modal.style.display = 'block';
                    
                    // 绑定关闭按钮事件
                    modal.querySelector('.close-btn').addEventListener('click', () => {
                        modal.style.display = 'none';
                        document.body.removeChild(modal);
                        // 刷新账号列表
                        loadAccounts();
                    });
                } else {
                    alert(`删除未使用账号失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('删除未使用账号请求失败:', error);
                alert('删除未使用账号请求失败，请查看控制台了解详情。');
            } finally {
                // 恢复按钮
                button.textContent = originalText;
                button.disabled = false;
            }
        }
        
        async function loadAccounts() {
            try {
                const response = await fetch('/api/accounts');
                const text = await response.text();
                const accounts = text.split('\n').filter(line => line.trim()).reverse();
                
                let verifiedCount = 0;
                const parsedAccounts = accounts.map(account => {
                    const info = parseAccountInfo(account);
                    if (info.isVerified) {
                        verifiedCount++;
                    }
                    return info;
                });
                
                // 更新统计信息
                document.getElementById('totalCount').textContent = accounts.length;
                document.getElementById('verifiedCount').textContent = verifiedCount;
                
                const today = new Date();
                const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
                const todayCount = accounts.filter(account => 
                    account.includes(todayStr)
                ).length;
                document.getElementById('todayCount').textContent = todayCount;
                
                // 更新账号列表
                const accountsList = document.getElementById('accountsList');
                accountsList.innerHTML = ''; // 清空现有内容
                
                // 为每个账号创建表格行
                for (const info of parsedAccounts) {
                    const row = document.createElement('tr');
                    if (info.isVerified) {
                        row.className = 'verified';
                    }
                    
                    // 创建表格行数据
                    row.innerHTML = `
                        <td class="account-time">${info.timestamp}</td>
                        <td>${info.account}</td>
                        <td>${info.password}</td>
                        <td class="role-name" data-account="${info.account}" data-timestamp="${info.timestamp}">-</td>
                        <td class="account-money" data-account="${info.account}" data-timestamp="${info.timestamp}">-</td>
                        <td class="last-login" data-account="${info.account}" data-timestamp="${info.timestamp}">-</td>
                        <td>${info.name}</td>
                        <td>${info.idcard}</td>
                    `;
                    
                    accountsList.appendChild(row);
                }
                
                // 绑定查询按钮事件
                document.getElementById('queryAllBtn').addEventListener('click', queryAllAccounts);
                
                // 绑定删除按钮事件
                document.getElementById('deleteInactiveBtn').addEventListener('click', deleteInactiveAccounts);
                document.getElementById('deleteUnusedBtn').addEventListener('click', deleteUnusedAccounts);
            } catch (error) {
                console.error('加载账号数据失败:', error);
            }
        }
        
        // 页面加载时不再自动加载数据，而是通过登录验证后加载
        // 登录验证通过后会调用这些函数
        
        // 添加表格排序功能
        document.getElementById('moneyHeader').addEventListener('click', () => sortTable('money'));
        document.getElementById('loginHeader').addEventListener('click', () => sortTable('login'));
        
        // 排序状态
        const sortState = {
            money: 0, // 0: 未排序, 1: 升序, -1: 降序
            login: 0
        };
        
        // 通用表格排序函数
        function sortTable(column) {
            const moneyHeader = document.getElementById('moneyHeader');
            const loginHeader = document.getElementById('loginHeader');
            const tbody = document.getElementById('accountsList');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // 重置其他列的排序状态
            if (column === 'money') {
                sortState.login = 0;
                loginHeader.classList.remove('sort-asc', 'sort-desc');
            } else if (column === 'login') {
                sortState.money = 0;
                moneyHeader.classList.remove('sort-asc', 'sort-desc');
            }
            
            // 更新当前列的排序状态
            sortState[column] = sortState[column] === 1 ? -1 : 1;
            
            // 更新排序图标
            const header = column === 'money' ? moneyHeader : loginHeader;
            if (sortState[column] === 1) {
                header.classList.remove('sort-desc');
                header.classList.add('sort-asc');
            } else {
                header.classList.remove('sort-asc');
                header.classList.add('sort-desc');
            }
            
            // 排序函数
            function getSortValue(row, columnType) {
                if (columnType === 'money') {
                    const moneyCell = row.querySelector('.account-money');
                    if (!moneyCell) return 0;
                    
                    const text = moneyCell.textContent.trim();
                    if (text === '-' || text === '查询中...' || text === '查询失败') return 0;
                    
                    // 提取¥符号后的数字
                    const match = text.match(/¥(\d+(\.\d+)?)/);
                    return match ? parseFloat(match[1]) : 0;
                } else if (columnType === 'login') {
                    const loginCell = row.querySelector('.last-login');
                    if (!loginCell) return 0;
                    
                    const text = loginCell.textContent.trim();
                    if (text === '-' || text === '查询中...' || text === '查询失败' || text === '未登录') return 0;
                    
                    // 将日期时间转换为时间戳
                    try {
                        return new Date(text).getTime();
                    } catch (e) {
                        return 0;
                    }
                }
                return 0;
            }
            
            // 排序行
            rows.sort((a, b) => {
                const valueA = getSortValue(a, column);
                const valueB = getSortValue(b, column);
                return (valueA - valueB) * sortState[column];
            });
            
            // 清空表格
            tbody.innerHTML = '';
            
            // 重新添加已排序的行
            rows.forEach(row => tbody.appendChild(row));
        }
        
        // 根据充值金额排序表格
        function sortTableByMoney() {
            sortTable('money');
        }
    </script>
</body>
</html> 